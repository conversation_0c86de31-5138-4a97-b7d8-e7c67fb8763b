# 简化版蜜罐监控程序

一个用Python编写的简单蜜罐程序，用于监控和记录网络连接尝试。

## 功能特性

- 🎯 多端口监听（SSH、Telnet、FTP、HTTP、HTTPS、RDP、SQL Server、MySQL等）
- 📝 详细的日志记录功能
- 🔄 日志文件自动轮转
- ⚙️ 灵活的配置选项
- 🖥️ 实时控制台输出
- 📊 结构化日志格式

## 新增日志功能

### 日志记录内容
程序会记录以下类型的事件：

1. **CONNECTION_ESTABLISHED** - 新连接建立
2. **DATA_RECEIVED** - 接收到客户端数据
3. **RESPONSE_SENT** - 发送响应给客户端
4. **CONNECTION_TIMEOUT** - 连接超时
5. **CONNECTION_ERROR** - 连接处理错误
6. **CONNECTION_CLOSED** - 连接关闭

### 日志信息包含
每条日志记录包含以下信息：
- 时间戳
- 事件类型
- 客户端IP地址和端口
- 目标端口和协议
- 传输的数据内容（如有）
- 额外的元数据信息

### 日志配置选项
在 `config.yaml` 中可以配置：
```yaml
logging:
  enabled: true                    # 是否启用日志记录
  log_file: "honeypot.log"        # 日志文件路径
  log_level: "INFO"               # 日志级别
  log_format: "%(asctime)s - %(levelname)s - %(message)s"
  max_file_size: 10485760         # 单个日志文件最大大小 (10MB)
  backup_count: 5                 # 保留的日志文件备份数量
```

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 创建配置文件
```bash
python main.py --create-config
```

### 3. 运行蜜罐
```bash
python main.py
```

### 4. 使用自定义配置
```bash
python main.py -c my_config.yaml
```

## 日志示例

```
2025-07-07 13:24:39,214 - INFO - [CONNECTION_ESTABLISHED] *************:12345 -> 22(SSH)
2025-07-07 13:24:39,215 - INFO - [DATA_RECEIVED] *************:12345 -> 22(SSH) | Data: SSH-2.0-OpenSSH_8.0 | Extra: {"data_length": 19, "data_type": "text"}
2025-07-07 13:24:39,215 - INFO - [RESPONSE_SENT] *************:12345 -> 22(SSH) | Data: SSH-2.0-OpenSSH_8.0 | Extra: {"response_length": 19}
2025-07-07 13:24:39,216 - INFO - [CONNECTION_CLOSED] *************:12345 -> 22(SSH) | Extra: {"session_duration": "short", "data_received": true, "response_sent": true}
```

## 测试日志功能

运行测试脚本来验证日志功能：
```bash
python test_logging.py
```

## 文件结构

```
simple-honeypot/
├── main.py              # 主程序文件
├── config.yaml          # 配置文件
├── requirements.txt     # Python依赖
├── test_logging.py      # 日志功能测试脚本
├── honeypot.log         # 日志文件（运行后生成）
└── README.md           # 说明文档
```

## 注意事项

1. 运行程序需要管理员权限（监听低端口号）
2. 确保防火墙允许相应端口的连接
3. 日志文件会自动轮转，避免占用过多磁盘空间
4. 建议在隔离的测试环境中运行

## 许可证

本项目仅供学习和研究使用，请勿用于非法用途。
