# 简化版蜜罐监控程序

一个用Python编写的简单蜜罐程序，用于监控和记录网络连接尝试。支持客户端-服务端架构，可以将蜜罐事件实时发送到中央服务端进行统一管理。

## 功能特性

- 🎯 多端口监听（SSH、Telnet、FTP、HTTP、HTTPS、RDP、SQL Server、MySQL等）
- 📝 详细的日志记录功能
- 🔄 日志文件自动轮转
- ⚙️ 灵活的配置选项
- 🖥️ 实时控制台输出
- 📊 结构化日志格式
- 🌐 客户端-服务端架构
- 📡 实时事件传输
- 🔄 自动重连机制
- 📈 集中化日志管理

## 系统架构

### 蜜罐客户端 (main.py)
- 监听指定端口，模拟各种服务
- 记录连接尝试到本地日志文件
- 实时发送事件记录到服务端
- 支持自动重连和错误恢复

### 蜜罐服务端 (honeypot_server.py)
- 接收来自多个蜜罐客户端的事件记录
- 将接收到的记录写入服务端日志
- 支持多客户端并发连接
- 提供连接状态监控

## 日志功能

### 日志记录内容
程序会记录以下类型的事件：

1. **CONNECTION_ESTABLISHED** - 新连接建立
2. **DATA_RECEIVED** - 接收到客户端数据
3. **RESPONSE_SENT** - 发送响应给客户端
4. **CONNECTION_TIMEOUT** - 连接超时
5. **CONNECTION_ERROR** - 连接处理错误
6. **CONNECTION_CLOSED** - 连接关闭

### 日志信息包含
每条日志记录包含以下信息：
- 时间戳
- 事件类型
- 客户端IP地址和端口
- 目标端口和协议
- 传输的数据内容（如有）
- 额外的元数据信息

### 客户端日志配置
在 `config.yaml` 中可以配置：
```yaml
logging:
  enabled: true                    # 是否启用日志记录
  log_file: "honeypot.log"        # 日志文件路径
  log_level: "INFO"               # 日志级别
  log_format: "%(asctime)s - %(levelname)s - %(message)s"
  max_file_size: 10485760         # 单个日志文件最大大小 (10MB)
  backup_count: 5                 # 保留的日志文件备份数量

server:
  enabled: true                   # 是否启用服务端连接
  host: "127.0.0.1"              # 服务端地址
  port: 8888                      # 服务端端口
  timeout: 5                      # 连接超时时间（秒）
  retry_attempts: 3               # 重试次数
  retry_delay: 1                  # 重试延迟（秒）
```

### 服务端日志配置
在 `server_config.yaml` 中可以配置：
```yaml
server:
  host: "0.0.0.0"                # 监听地址
  port: 8888                      # 监听端口
  max_connections: 100            # 最大连接数

logging:
  enabled: true                   # 是否启用日志记录
  log_file: "honeypot_server.log" # 日志文件路径
  log_level: "INFO"              # 日志级别
  log_format: "%(asctime)s - %(levelname)s - %(message)s"
  max_file_size: 10485760        # 单个日志文件最大大小 (10MB)
  backup_count: 5                # 保留的日志文件备份数量
```

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 创建配置文件
```bash
# 创建蜜罐客户端配置
python main.py --create-config

# 创建蜜罐服务端配置
python honeypot_server.py --create-config
```

### 3. 启动系统

#### 方式一：仅运行蜜罐客户端（本地日志）
```bash
# 如果不需要服务端，可以在config.yaml中设置 server.enabled: false
python main.py
```

#### 方式二：运行完整的客户端-服务端系统
```bash
# 1. 首先启动服务端
python honeypot_server.py

# 2. 在另一个终端启动客户端
python main.py
```

### 4. 使用自定义配置
```bash
# 客户端使用自定义配置
python main.py -c my_config.yaml

# 服务端使用自定义配置
python honeypot_server.py -c my_server_config.yaml
```

## 日志示例

### 客户端日志 (honeypot.log)
```
2025-07-09 09:52:44,178 - INFO - [CONNECTION_ESTABLISHED] *************:12345 -> 22(SSH)
2025-07-09 09:52:44,679 - INFO - [DATA_RECEIVED] *************:12345 -> 22(SSH) | Data: SSH-2.0-OpenSSH_8.0 | Extra: {"data_length": 19, "data_type": "text"}
2025-07-09 09:52:45,177 - INFO - [RESPONSE_SENT] *************:12345 -> 22(SSH) | Data: SSH-2.0-OpenSSH_8.0 | Extra: {"response_length": 19}
2025-07-09 09:52:45,679 - INFO - [CONNECTION_CLOSED] *************:12345 -> 22(SSH) | Extra: {"session_duration": "short", "data_received": true, "response_sent": true}
```

### 服务端日志 (honeypot_server.log)
```
2025-07-09 09:52:44,181 - INFO - 蜜罐客户端连接: 127.0.0.1:11953
2025-07-09 09:52:44,182 - INFO - [HONEYPOT-127.0.0.1:11953] [CONNECTION_ESTABLISHED] *************:12345 -> 22(SSH)
2025-07-09 09:52:44,681 - INFO - [HONEYPOT-127.0.0.1:11953] [DATA_RECEIVED] *************:12345 -> 22(SSH) | Data: SSH-2.0-OpenSSH_8.0 | Extra: {"data_length": 19, "data_type": "text"}
2025-07-09 09:52:45,180 - INFO - [HONEYPOT-127.0.0.1:11953] [RESPONSE_SENT] *************:12345 -> 22(SSH) | Data: SSH-2.0-OpenSSH_8.0 | Extra: {"response_length": 19}
2025-07-09 09:52:51,286 - INFO - 蜜罐客户端断开: 127.0.0.1:11953 (共接收 10 条记录)
```

## 文件结构

```
simple-honeypot/
├── main.py                    # 蜜罐客户端主程序
├── honeypot_server.py         # 蜜罐服务端程序
├── config.yaml               # 客户端配置文件
├── server_config.yaml        # 服务端配置文件
├── requirements.txt          # Python依赖
├── honeypot.log             # 客户端日志文件（运行后生成）
├── honeypot_server.log      # 服务端日志文件（运行后生成）
└── README.md                # 说明文档
```

## 工作流程

1. **服务端启动**: 蜜罐服务端启动并监听指定端口，等待客户端连接
2. **客户端启动**: 蜜罐客户端启动，连接到服务端，并开始监听蜜罐端口
3. **事件触发**: 当有攻击者连接到蜜罐端口时，客户端记录事件
4. **双重记录**:
   - 客户端将事件记录到本地日志文件
   - 客户端同时将事件发送到服务端
5. **服务端处理**: 服务端接收事件记录并写入服务端日志文件
6. **实时监控**: 管理员可以同时监控客户端和服务端的日志输出

## 注意事项

1. **权限要求**: 运行蜜罐客户端需要管理员权限（监听低端口号）
2. **网络配置**: 确保防火墙允许相应端口的连接
3. **服务端端口**: 确保服务端端口（默认8888）未被占用
4. **日志管理**: 日志文件会自动轮转，避免占用过多磁盘空间
5. **安全环境**: 建议在隔离的测试环境中运行
6. **网络连通性**: 确保客户端能够访问服务端的IP和端口
7. **多客户端**: 支持多个蜜罐客户端连接到同一个服务端

## 故障排除

### 客户端无法连接到服务端
- 检查服务端是否正常启动
- 验证网络连通性
- 确认配置文件中的服务端地址和端口正确
- 检查防火墙设置

### 端口被占用
- 使用 `netstat -an` 检查端口占用情况
- 修改配置文件中的端口设置
- 停止占用端口的其他程序

## 许可证

本项目仅供学习和研究使用，请勿用于非法用途。
