# config.yaml - 简化版蜜罐配置文件

# 监听的端口列表
ports:
  - 22    # SSH
  - 23    # Telnet
  - 21    # FTP
  - 80    # HTTP
  - 443   # HTTPS
  - 3389  # RDP
  - 1433  # SQL Server
  - 3306  # MySQL

# 绑定地址 (0.0.0.0 表示监听所有网络接口)
bind_address: "0.0.0.0"

# 连接超时时间（秒）
connection_timeout: 30

# 日志配置
logging:
  enabled: true                    # 是否启用日志记录
  log_file: "honeypot.log"        # 日志文件路径
  log_level: "INFO"               # 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  log_format: "%(asctime)s - %(levelname)s - %(message)s"  # 日志格式
  max_file_size: 10485760         # 单个日志文件最大大小 (10MB)
  backup_count: 5                 # 保留的日志文件备份数量