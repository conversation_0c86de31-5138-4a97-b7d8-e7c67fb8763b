# Python字节码文件
__pycache__/
*.py[cod]
*$py.class

# C扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 翻译
*.mo
*.pot

# Django
*.log
local_settings.py
db.sqlite3

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/

# IDE相关
.idea/
.vscode/
*.swp
*.swo
