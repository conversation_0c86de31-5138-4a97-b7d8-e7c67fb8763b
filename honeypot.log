2025-07-07 13:24:02,631 - INFO - 蜜罐日志系统初始化完成
2025-07-07 13:24:39,213 - INFO - 蜜罐日志系统初始化完成
2025-07-07 13:24:39,214 - INFO - [CONNECTION_ESTABLISHED] *************:12345 -> 22(SSH)
2025-07-07 13:24:39,215 - INFO - [DATA_RECEIVED] *************:12345 -> 22(SSH) | Data: SSH-2.0-OpenSSH_8.0 | Extra: {"data_length": 19, "data_type": "text"}
2025-07-07 13:24:39,215 - INFO - [RESPONSE_SENT] *************:12345 -> 22(SSH) | Data: SSH-2.0-OpenSSH_8.0 | Extra: {"response_length": 19}
2025-07-07 13:24:39,216 - INFO - [CONNECTION_CLOSED] *************:12345 -> 22(SSH) | Extra: {"session_duration": "short", "data_received": true, "response_sent": true}
2025-07-07 13:24:39,216 - INFO - [CONNECTION_ESTABLISHED] *********:54321 -> 80(HTTP)
2025-07-07 13:24:39,218 - INFO - [DATA_RECEIVED] *********:54321 -> 80(HTTP) | Data: GET / HTTP/1.1

Host: example.com



 | Extra: {"data_length": 35, "data_type": "text"}
2025-07-07 13:24:39,219 - INFO - [RESPONSE_SENT] *********:54321 -> 80(HTTP) | Data: HTTP/1.1 404 Not Found

Server: nginx/1.18.0

Content-Length: 0

 | Extra: {"response_length": 67}
2025-07-07 13:24:39,219 - INFO - [CONNECTION_CLOSED] *********:54321 -> 80(HTTP) | Extra: {"session_duration": "short", "data_received": true, "response_sent": true}
2025-07-07 13:24:39,219 - INFO - [CONNECTION_ESTABLISHED] ************:33333 -> 3389(RDP)
2025-07-07 13:24:39,219 - INFO - [CONNECTION_TIMEOUT] ************:33333 -> 3389(RDP)
