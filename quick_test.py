#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试蜜罐系统的脚本
"""

import asyncio
from main import SimpleHoneypot


async def quick_test():
    """快速测试蜜罐功能"""
    print("🧪 快速测试蜜罐系统...")
    
    # 创建蜜罐实例
    honeypot = SimpleHoneypot()
    
    print("📝 模拟蜜罐事件...")
    
    # 模拟一些事件
    honeypot.log_connection_event('CONNECTION_ESTABLISHED', '192.168.1.100', 12345, 22, 'SSH')
    await asyncio.sleep(0.1)
    
    honeypot.log_connection_event('DATA_RECEIVED', '192.168.1.100', 12345, 22, 'SSH', 
                                'SSH-2.0-OpenSSH_8.0', {'data_length': 19, 'data_type': 'text'})
    await asyncio.sleep(0.1)
    
    honeypot.log_connection_event('RESPONSE_SENT', '192.168.1.100', 12345, 22, 'SSH',
                                'SSH-2.0-OpenSSH_8.0', {'response_length': 19})
    await asyncio.sleep(0.1)
    
    honeypot.log_connection_event('CONNECTION_CLOSED', '192.168.1.100', 12345, 22, 'SSH',
                                None, {'session_duration': 'short', 'data_received': True, 'response_sent': True})
    
    print("✅ 测试完成！")
    print("📋 请检查以下文件：")
    print("   - honeypot.log (客户端日志)")
    print("   - honeypot_server.log (服务端日志，如果服务端正在运行)")
    
    # 等待异步任务完成
    await asyncio.sleep(1)


if __name__ == "__main__":
    asyncio.run(quick_test())
