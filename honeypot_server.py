#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蜜罐服务端程序
用于接收蜜罐客户端发送的记录并记录到日志中
"""

import asyncio
import json
import logging
import yaml
from datetime import datetime
from pathlib import Path
from logging.handlers import RotatingFileHandler


class HoneypotServer:
    def __init__(self, config_file='server_config.yaml'):
        self.config = self.load_config(config_file)
        self.server = None
        self.running = False
        self.setup_logging()
        self.clients = {}  # 存储连接的客户端信息
    
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            print(f"配置文件 {config_file} 不存在，使用默认配置")
            return self.get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """默认配置"""
        return {
            'server': {
                'host': '0.0.0.0',
                'port': 8888,
                'max_connections': 100
            },
            'logging': {
                'enabled': True,
                'log_file': 'honeypot_server.log',
                'log_level': 'INFO',
                'log_format': '%(asctime)s - %(levelname)s - %(message)s',
                'max_file_size': 10485760,  # 10MB
                'backup_count': 5
            }
        }
    
    def create_default_config(self, config_file='server_config.yaml'):
        """创建默认配置文件"""
        default_config = self.get_default_config()
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"已创建默认服务端配置文件: {config_file}")
    
    def setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})
        
        if not log_config.get('enabled', True):
            return
        
        # 创建logger
        self.logger = logging.getLogger('honeypot_server')
        self.logger.setLevel(getattr(logging, log_config.get('log_level', 'INFO')))
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 创建文件处理器
        log_file = log_config.get('log_file', 'honeypot_server.log')
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=log_config.get('max_file_size', 10485760),  # 10MB
            backupCount=log_config.get('backup_count', 5),
            encoding='utf-8'
        )
        
        # 设置日志格式
        log_format = log_config.get('log_format', '%(asctime)s - %(levelname)s - %(message)s')
        formatter = logging.Formatter(log_format)
        file_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        
        # 也添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        self.logger.info("蜜罐服务端日志系统初始化完成")
    
    async def handle_client(self, reader, writer):
        """处理客户端连接"""
        client_addr = writer.get_extra_info('peername')
        client_ip = client_addr[0] if client_addr else 'unknown'
        client_port = client_addr[1] if client_addr else 'unknown'
        client_id = f"{client_ip}:{client_port}"
        
        self.clients[client_id] = {
            'connected_at': datetime.now(),
            'records_received': 0
        }
        
        self.logger.info(f"蜜罐客户端连接: {client_id}")
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 蜜罐客户端连接: {client_id}")
        
        try:
            while True:
                # 读取消息长度（4字节）
                length_data = await reader.readexactly(4)
                if not length_data:
                    break
                
                message_length = int.from_bytes(length_data, byteorder='big')
                
                # 读取消息内容
                message_data = await reader.readexactly(message_length)
                if not message_data:
                    break
                
                # 解析JSON消息
                try:
                    message = json.loads(message_data.decode('utf-8'))
                    await self.process_honeypot_record(message, client_id)
                    self.clients[client_id]['records_received'] += 1
                    
                    # 发送确认响应
                    response = {'status': 'success', 'timestamp': datetime.now().isoformat()}
                    response_data = json.dumps(response, ensure_ascii=False).encode('utf-8')
                    response_length = len(response_data).to_bytes(4, byteorder='big')
                    
                    writer.write(response_length + response_data)
                    await writer.drain()
                    
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON解析错误 from {client_id}: {e}")
                    # 发送错误响应
                    error_response = {'status': 'error', 'message': 'Invalid JSON'}
                    error_data = json.dumps(error_response).encode('utf-8')
                    error_length = len(error_data).to_bytes(4, byteorder='big')
                    writer.write(error_length + error_data)
                    await writer.drain()
                
        except asyncio.IncompleteReadError:
            # 客户端断开连接
            pass
        except Exception as e:
            self.logger.error(f"处理客户端 {client_id} 时出错: {e}")
        
        finally:
            try:
                writer.close()
                await writer.wait_closed()
            except:
                pass
            
            if client_id in self.clients:
                records_count = self.clients[client_id]['records_received']
                del self.clients[client_id]
                self.logger.info(f"蜜罐客户端断开: {client_id} (共接收 {records_count} 条记录)")
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 蜜罐客户端断开: {client_id} (共接收 {records_count} 条记录)")
    
    async def process_honeypot_record(self, record, client_id):
        """处理蜜罐记录"""
        try:
            # 记录到服务端日志
            log_message = f"[HONEYPOT-{client_id}] [{record.get('event_type', 'UNKNOWN')}] "
            log_message += f"{record.get('client_ip', 'unknown')}:{record.get('client_port', 'unknown')} -> "
            log_message += f"{record.get('target_port', 'unknown')}({record.get('protocol', 'unknown')})"
            
            if record.get('data'):
                log_message += f" | Data: {record['data'][:100]}"
            
            if record.get('extra_info'):
                log_message += f" | Extra: {json.dumps(record['extra_info'], ensure_ascii=False)}"
            
            self.logger.info(log_message)
            
            # 实时显示到控制台
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"[{timestamp}] {log_message}")
            
        except Exception as e:
            self.logger.error(f"处理蜜罐记录时出错: {e}")
    
    async def start(self):
        """启动服务端"""
        if self.running:
            print("服务端已经在运行中")
            return
        
        server_config = self.config.get('server', {})
        host = server_config.get('host', '0.0.0.0')
        port = server_config.get('port', 8888)
        
        print("=" * 60)
        print("蜜罐服务端启动中...")
        print("=" * 60)
        
        try:
            self.server = await asyncio.start_server(
                self.handle_client,
                host=host,
                port=port,
                reuse_address=True
            )
            
            self.running = True
            self.logger.info(f"蜜罐服务端启动成功，监听 {host}:{port}")
            print(f"🚀 蜜罐服务端启动成功")
            print(f"监听地址: {host}:{port}")
            print(f"等待蜜罐客户端连接... (按 Ctrl+C 退出)\n")
            
            # 等待中断信号
            try:
                async with self.server:
                    await self.server.serve_forever()
            except KeyboardInterrupt:
                print("\n收到中断信号，正在停止服务端...")
                
        except Exception as e:
            print(f"启动服务端失败: {e}")
            self.logger.error(f"启动服务端失败: {e}")
    
    async def stop(self):
        """停止服务端"""
        if not self.running:
            return
        
        print("正在停止蜜罐服务端...")
        self.running = False
        
        if self.server:
            self.server.close()
            await self.server.wait_closed()
        
        self.logger.info("蜜罐服务端已停止")
        print("蜜罐服务端已停止")


def main():
    """主函数"""
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='蜜罐服务端程序')
    parser.add_argument('-c', '--config', default='server_config.yaml',
                       help='配置文件路径 (默认: server_config.yaml)')
    parser.add_argument('--create-config', action='store_true',
                       help='创建默认配置文件')
    
    args = parser.parse_args()
    
    # 创建配置文件
    if args.create_config:
        server = HoneypotServer()
        server.create_default_config(args.config)
        return
    
    # 检查配置文件是否存在
    if not Path(args.config).exists():
        print(f"配置文件 {args.config} 不存在")
        print(f"使用 --create-config 创建默认配置文件")
        print(f"示例: python {sys.argv[0]} --create-config")
        return
    
    # 创建并启动服务端
    server = HoneypotServer(args.config)
    
    try:
        asyncio.run(server.start())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("程序退出")


if __name__ == "__main__":
    main()
