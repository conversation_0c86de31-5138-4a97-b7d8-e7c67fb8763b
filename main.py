# simple_honeypot.py
import asyncio
import yaml
import socket
from datetime import datetime
from pathlib import Path

class SimpleHoneypot:
    def __init__(self, config_file='config.yaml'):
        self.config = self.load_config(config_file)
        self.servers = {}
        self.running = False
    
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            print(f"配置文件 {config_file} 不存在，使用默认配置")
            return self.get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """默认配置"""
        return {
            'ports': [22, 23, 80, 443, 3389],
            'bind_address': '0.0.0.0',
            'connection_timeout': 30
        }
    
    def create_default_config(self, config_file='config.yaml'):
        """创建默认配置文件"""
        default_config = {
            'ports': [22, 23, 80, 443, 3389],
            'bind_address': '0.0.0.0',
            'connection_timeout': 30
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"已创建默认配置文件: {config_file}")
    
    async def handle_connection(self, reader, writer, port):
        """处理连接"""
        # 获取客户端信息
        client_addr = writer.get_extra_info('peername')
        client_ip = client_addr[0] if client_addr else 'unknown'
        client_port = client_addr[1] if client_addr else 'unknown'
        
        # 获取当前时间
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 输出连接信息
        print(f"[{timestamp}] 新连接: {client_ip}:{client_port} -> 端口 {port}")
        
        try:
            # 等待客户端发送数据（最多等待5秒）
            try:
                data = await asyncio.wait_for(reader.read(1024), timeout=5.0)
                if data:
                    # 尝试解码数据
                    try:
                        decoded_data = data.decode('utf-8', errors='ignore').strip()
                        print(f"[{timestamp}] 接收数据: {client_ip}:{client_port} -> '{decoded_data[:100]}'")
                    except:
                        print(f"[{timestamp}] 接收数据: {client_ip}:{client_port} -> {len(data)} 字节 (二进制)")
            except asyncio.TimeoutError:
                print(f"[{timestamp}] 连接超时: {client_ip}:{client_port}")
            
            # 根据端口发送简单响应
            response = self.get_response_for_port(port)
            if response:
                writer.write(response.encode() + b'\r\n')
                await writer.drain()
                print(f"[{timestamp}] 发送响应: {client_ip}:{client_port} -> '{response}'")
            
            # 短暂延迟后关闭连接
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"[{timestamp}] 处理连接出错: {client_ip}:{client_port} -> {e}")
        
        finally:
            # 关闭连接
            try:
                writer.close()
                await writer.wait_closed()
                print(f"[{timestamp}] 连接关闭: {client_ip}:{client_port}")
            except:
                pass
    
    def get_response_for_port(self, port):
        """根据端口返回相应的响应"""
        responses = {
            22: "SSH-2.0-OpenSSH_8.0",
            23: "Welcome to Telnet\r\nlogin: ",
            21: "220 FTP server ready",
            80: "HTTP/1.1 404 Not Found\r\nServer: nginx/1.18.0\r\nContent-Length: 0\r\n",
            443: "",  # HTTPS通常需要SSL握手，这里不响应
            3389: "",  # RDP协议复杂，这里不响应
            1433: "",  # SQL Server
            3306: ""   # MySQL
        }
        return responses.get(port, "")
    
    def get_port_protocol(self, port):
        """获取端口对应的协议名称"""
        protocols = {
            22: "SSH",
            23: "Telnet", 
            21: "FTP",
            80: "HTTP",
            443: "HTTPS",
            3389: "RDP",
            1433: "MSSQL",
            3306: "MySQL"
        }
        return protocols.get(port, "Unknown")
    
    def check_port_available(self, port):
        """检查端口是否可用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind((self.config['bind_address'], port))
            sock.close()
            return True
        except OSError:
            return False
    
    async def start_port_listener(self, port):
        """启动单个端口监听"""
        try:
            server = await asyncio.start_server(
                lambda r, w: self.handle_connection(r, w, port),
                host=self.config['bind_address'],
                port=port,
                reuse_address=True
            )
            
            self.servers[port] = server
            protocol = self.get_port_protocol(port)
            print(f"✓ 开始监听端口 {port} ({protocol})")
            
        except Exception as e:
            print(f"✗ 无法监听端口 {port}: {e}")
    
    async def start(self):
        """启动蜜罐"""
        if self.running:
            print("蜜罐已经在运行中")
            return
        
        print("=" * 50)
        print("简化版蜜罐监控程序启动中...")
        print("=" * 50)
        
        # 检查端口可用性
        available_ports = []
        for port in self.config['ports']:
            if self.check_port_available(port):
                available_ports.append(port)
            else:
                print(f"⚠ 端口 {port} 已被占用，跳过")
        
        if not available_ports:
            print("没有可用的端口，程序退出")
            return
        
        # 启动端口监听
        for port in available_ports:
            await self.start_port_listener(port)
        
        if self.servers:
            self.running = True
            print(f"\n🎯 蜜罐启动成功，正在监听 {len(self.servers)} 个端口")
            print(f"监听地址: {self.config['bind_address']}")
            print(f"监听端口: {list(self.servers.keys())}")
            print("\n等待连接中... (按 Ctrl+C 退出)\n")
            
            # 等待中断信号
            try:
                while self.running:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                print("\n收到中断信号，正在停止...")
        else:
            print("没有成功启动任何端口监听")
    
    async def stop(self):
        """停止蜜罐"""
        if not self.running:
            return
        
        print("正在停止蜜罐...")
        self.running = False
        
        # 关闭所有服务器
        for port, server in self.servers.items():
            print(f"停止监听端口 {port}")
            server.close()
            await server.wait_closed()
        
        self.servers.clear()
        print("蜜罐已停止")


def main():
    """主函数"""
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='简化版蜜罐监控程序')
    parser.add_argument('-c', '--config', default='config.yaml', 
                       help='配置文件路径 (默认: config.yaml)')
    parser.add_argument('--create-config', action='store_true',
                       help='创建默认配置文件')
    
    args = parser.parse_args()
    
    # 创建配置文件
    if args.create_config:
        honeypot = SimpleHoneypot()
        honeypot.create_default_config(args.config)
        return
    
    # 检查配置文件是否存在
    if not Path(args.config).exists():
        print(f"配置文件 {args.config} 不存在")
        print(f"使用 --create-config 创建默认配置文件")
        print(f"示例: python {sys.argv[0]} --create-config")
        return
    
    # 创建并启动蜜罐
    honeypot = SimpleHoneypot(args.config)
    
    try:
        asyncio.run(honeypot.start())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("程序退出")


if __name__ == "__main__":
    main()