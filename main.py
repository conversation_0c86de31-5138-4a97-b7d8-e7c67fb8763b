# simple_honeypot.py
import asyncio
import yaml
import socket
import logging
import json
from datetime import datetime
from pathlib import Path

class SimpleHoneypot:
    def __init__(self, config_file='config.yaml'):
        self.config = self.load_config(config_file)
        self.servers = {}
        self.running = False
        self.server_writer = None
        self.server_reader = None
        self.setup_logging()
        self.setup_server_connection()
    
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            print(f"配置文件 {config_file} 不存在，使用默认配置")
            return self.get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.get_default_config()
    
    def get_default_config(self):
        """默认配置"""
        return {
            'ports': [22, 23, 80, 443, 3389],
            'bind_address': '0.0.0.0',
            'connection_timeout': 30,
            'logging': {
                'enabled': True,
                'log_file': 'honeypot.log',
                'log_level': 'INFO',
                'log_format': '%(asctime)s - %(levelname)s - %(message)s',
                'max_file_size': 10485760,  # 10MB
                'backup_count': 5
            },
            'server': {
                'enabled': True,
                'host': '127.0.0.1',
                'port': 8888,
                'timeout': 5,
                'retry_attempts': 3,
                'retry_delay': 1
            }
        }
    
    def create_default_config(self, config_file='config.yaml'):
        """创建默认配置文件"""
        default_config = {
            'ports': [22, 23, 80, 443, 3389],
            'bind_address': '0.0.0.0',
            'connection_timeout': 30,
            'logging': {
                'enabled': True,
                'log_file': 'honeypot.log',
                'log_level': 'INFO',
                'log_format': '%(asctime)s - %(levelname)s - %(message)s',
                'max_file_size': 10485760,  # 10MB
                'backup_count': 5
            },
            'server': {
                'enabled': True,
                'host': '127.0.0.1',
                'port': 8888,
                'timeout': 5,
                'retry_attempts': 3,
                'retry_delay': 1
            }
        }

        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)

        print(f"已创建默认配置文件: {config_file}")

    def setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})

        if not log_config.get('enabled', True):
            return

        # 创建logger
        self.logger = logging.getLogger('honeypot')
        self.logger.setLevel(getattr(logging, log_config.get('log_level', 'INFO')))

        # 清除现有的处理器
        self.logger.handlers.clear()

        # 创建文件处理器
        log_file = log_config.get('log_file', 'honeypot.log')
        from logging.handlers import RotatingFileHandler

        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=log_config.get('max_file_size', 10485760),  # 10MB
            backupCount=log_config.get('backup_count', 5),
            encoding='utf-8'
        )

        # 设置日志格式
        log_format = log_config.get('log_format', '%(asctime)s - %(levelname)s - %(message)s')
        formatter = logging.Formatter(log_format)
        file_handler.setFormatter(formatter)

        # 添加处理器
        self.logger.addHandler(file_handler)

        # 也添加控制台处理器（可选）
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        self.logger.info("蜜罐日志系统初始化完成")

    def setup_server_connection(self):
        """设置服务端连接"""
        self.server_config = self.config.get('server', {})
        if not self.server_config.get('enabled', True):
            self.logger.info("服务端连接已禁用")
            return

        # 服务端连接将在需要时建立
        self.logger.info(f"服务端连接配置: {self.server_config.get('host')}:{self.server_config.get('port')}")

    async def connect_to_server(self):
        """连接到服务端"""
        if not self.server_config.get('enabled', True):
            return False

        if self.server_writer and not self.server_writer.is_closing():
            return True

        try:
            host = self.server_config.get('host', '127.0.0.1')
            port = self.server_config.get('port', 8888)
            timeout = self.server_config.get('timeout', 5)

            self.server_reader, self.server_writer = await asyncio.wait_for(
                asyncio.open_connection(host, port),
                timeout=timeout
            )

            self.logger.info(f"已连接到蜜罐服务端: {host}:{port}")
            return True

        except Exception as e:
            self.logger.warning(f"连接服务端失败: {e}")
            return False

    async def send_to_server(self, record):
        """发送记录到服务端"""
        if not self.server_config.get('enabled', True):
            return

        retry_attempts = self.server_config.get('retry_attempts', 3)
        retry_delay = self.server_config.get('retry_delay', 1)

        for attempt in range(retry_attempts):
            try:
                # 确保连接到服务端
                if not await self.connect_to_server():
                    if attempt < retry_attempts - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        return

                # 发送数据
                message_data = json.dumps(record, ensure_ascii=False).encode('utf-8')
                message_length = len(message_data).to_bytes(4, byteorder='big')

                self.server_writer.write(message_length + message_data)
                await self.server_writer.drain()

                # 读取响应
                response_length_data = await asyncio.wait_for(
                    self.server_reader.readexactly(4),
                    timeout=self.server_config.get('timeout', 5)
                )
                response_length = int.from_bytes(response_length_data, byteorder='big')

                response_data = await asyncio.wait_for(
                    self.server_reader.readexactly(response_length),
                    timeout=self.server_config.get('timeout', 5)
                )

                response = json.loads(response_data.decode('utf-8'))
                if response.get('status') == 'success':
                    return  # 成功发送
                else:
                    self.logger.warning(f"服务端返回错误: {response.get('message', 'Unknown error')}")

            except Exception as e:
                self.logger.warning(f"发送到服务端失败 (尝试 {attempt + 1}/{retry_attempts}): {e}")

                # 关闭连接以便重试
                if self.server_writer:
                    try:
                        self.server_writer.close()
                        await self.server_writer.wait_closed()
                    except:
                        pass
                    self.server_writer = None
                    self.server_reader = None

                if attempt < retry_attempts - 1:
                    await asyncio.sleep(retry_delay)

    def log_connection_event(self, event_type, client_ip, client_port, target_port, protocol, data=None, extra_info=None):
        """记录连接事件到日志"""
        if not hasattr(self, 'logger'):
            return

        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'client_ip': client_ip,
            'client_port': client_port,
            'target_port': target_port,
            'protocol': protocol,
        }

        if data:
            log_entry['data'] = data

        if extra_info:
            log_entry['extra_info'] = extra_info

        # 记录结构化日志
        log_message = f"[{event_type}] {client_ip}:{client_port} -> {target_port}({protocol})"
        if data:
            log_message += f" | Data: {data[:100]}"
        if extra_info:
            log_message += f" | Extra: {json.dumps(extra_info, ensure_ascii=False)}"

        self.logger.info(log_message)

        # 异步发送到服务端
        asyncio.create_task(self.send_to_server(log_entry))

    async def handle_connection(self, reader, writer, port):
        """处理连接"""
        # 获取客户端信息
        client_addr = writer.get_extra_info('peername')
        client_ip = client_addr[0] if client_addr else 'unknown'
        client_port = client_addr[1] if client_addr else 'unknown'
        protocol = self.get_port_protocol(port)

        # 获取当前时间
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 记录新连接日志
        self.log_connection_event('CONNECTION_ESTABLISHED', client_ip, client_port, port, protocol)

        # 输出连接信息
        print(f"[{timestamp}] 新连接: {client_ip}:{client_port} -> 端口 {port}")

        received_data = None
        response_sent = None

        try:
            # 等待客户端发送数据（最多等待5秒）
            try:
                data = await asyncio.wait_for(reader.read(1024), timeout=5.0)
                if data:
                    # 尝试解码数据
                    try:
                        decoded_data = data.decode('utf-8', errors='ignore').strip()
                        received_data = decoded_data
                        print(f"[{timestamp}] 接收数据: {client_ip}:{client_port} -> '{decoded_data[:100]}'")

                        # 记录数据接收日志
                        self.log_connection_event('DATA_RECEIVED', client_ip, client_port, port, protocol,
                                                decoded_data[:500], {'data_length': len(data), 'data_type': 'text'})
                    except:
                        received_data = f"{len(data)} bytes (binary)"
                        print(f"[{timestamp}] 接收数据: {client_ip}:{client_port} -> {len(data)} 字节 (二进制)")

                        # 记录二进制数据接收日志
                        self.log_connection_event('DATA_RECEIVED', client_ip, client_port, port, protocol,
                                                f"Binary data ({len(data)} bytes)", {'data_length': len(data), 'data_type': 'binary'})
            except asyncio.TimeoutError:
                print(f"[{timestamp}] 连接超时: {client_ip}:{client_port}")
                # 记录连接超时日志
                self.log_connection_event('CONNECTION_TIMEOUT', client_ip, client_port, port, protocol)

            # 根据端口发送简单响应
            response = self.get_response_for_port(port)
            if response:
                writer.write(response.encode() + b'\r\n')
                await writer.drain()
                response_sent = response
                print(f"[{timestamp}] 发送响应: {client_ip}:{client_port} -> '{response}'")

                # 记录响应发送日志
                self.log_connection_event('RESPONSE_SENT', client_ip, client_port, port, protocol,
                                        response, {'response_length': len(response)})

            # 短暂延迟后关闭连接
            await asyncio.sleep(1)

        except Exception as e:
            print(f"[{timestamp}] 处理连接出错: {client_ip}:{client_port} -> {e}")
            # 记录错误日志
            self.log_connection_event('CONNECTION_ERROR', client_ip, client_port, port, protocol,
                                    str(e), {'error_type': type(e).__name__})

        finally:
            # 关闭连接
            try:
                writer.close()
                await writer.wait_closed()
                print(f"[{timestamp}] 连接关闭: {client_ip}:{client_port}")

                # 记录连接关闭日志，包含会话摘要
                session_summary = {
                    'session_duration': 'short',
                    'data_received': bool(received_data),
                    'response_sent': bool(response_sent)
                }
                self.log_connection_event('CONNECTION_CLOSED', client_ip, client_port, port, protocol,
                                        None, session_summary)
            except:
                pass
    
    def get_response_for_port(self, port):
        """根据端口返回相应的响应"""
        responses = {
            22: "SSH-2.0-OpenSSH_8.0",
            23: "Welcome to Telnet\r\nlogin: ",
            21: "220 FTP server ready",
            80: "HTTP/1.1 404 Not Found\r\nServer: nginx/1.18.0\r\nContent-Length: 0\r\n",
            443: "",  # HTTPS通常需要SSL握手，这里不响应
            3389: "",  # RDP协议复杂，这里不响应
            1433: "",  # SQL Server
            3306: ""   # MySQL
        }
        return responses.get(port, "")
    
    def get_port_protocol(self, port):
        """获取端口对应的协议名称"""
        protocols = {
            22: "SSH",
            23: "Telnet", 
            21: "FTP",
            80: "HTTP",
            443: "HTTPS",
            3389: "RDP",
            1433: "MSSQL",
            3306: "MySQL"
        }
        return protocols.get(port, "Unknown")
    
    def check_port_available(self, port):
        """检查端口是否可用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind((self.config['bind_address'], port))
            sock.close()
            return True
        except OSError:
            return False
    
    async def start_port_listener(self, port):
        """启动单个端口监听"""
        try:
            server = await asyncio.start_server(
                lambda r, w: self.handle_connection(r, w, port),
                host=self.config['bind_address'],
                port=port,
                reuse_address=True
            )
            
            self.servers[port] = server
            protocol = self.get_port_protocol(port)
            print(f"✓ 开始监听端口 {port} ({protocol})")
            
        except Exception as e:
            print(f"✗ 无法监听端口 {port}: {e}")
    
    async def start(self):
        """启动蜜罐"""
        if self.running:
            print("蜜罐已经在运行中")
            return
        
        print("=" * 50)
        print("简化版蜜罐监控程序启动中...")
        print("=" * 50)
        
        # 检查端口可用性
        available_ports = []
        for port in self.config['ports']:
            if self.check_port_available(port):
                available_ports.append(port)
            else:
                print(f"⚠ 端口 {port} 已被占用，跳过")
        
        if not available_ports:
            print("没有可用的端口，程序退出")
            return
        
        # 启动端口监听
        for port in available_ports:
            await self.start_port_listener(port)
        
        if self.servers:
            self.running = True
            print(f"\n🎯 蜜罐启动成功，正在监听 {len(self.servers)} 个端口")
            print(f"监听地址: {self.config['bind_address']}")
            print(f"监听端口: {list(self.servers.keys())}")
            print("\n等待连接中... (按 Ctrl+C 退出)\n")
            
            # 等待中断信号
            try:
                while self.running:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                print("\n收到中断信号，正在停止...")
        else:
            print("没有成功启动任何端口监听")
    
    async def stop(self):
        """停止蜜罐"""
        if not self.running:
            return

        print("正在停止蜜罐...")
        self.running = False

        # 关闭所有服务器
        for port, server in self.servers.items():
            print(f"停止监听端口 {port}")
            server.close()
            await server.wait_closed()

        self.servers.clear()

        # 关闭服务端连接
        if self.server_writer:
            try:
                self.server_writer.close()
                await self.server_writer.wait_closed()
                self.logger.info("已断开服务端连接")
            except:
                pass

        print("蜜罐已停止")


def main():
    """主函数"""
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description='简化版蜜罐监控程序')
    parser.add_argument('-c', '--config', default='config.yaml', 
                       help='配置文件路径 (默认: config.yaml)')
    parser.add_argument('--create-config', action='store_true',
                       help='创建默认配置文件')
    
    args = parser.parse_args()
    
    # 创建配置文件
    if args.create_config:
        honeypot = SimpleHoneypot()
        honeypot.create_default_config(args.config)
        return
    
    # 检查配置文件是否存在
    if not Path(args.config).exists():
        print(f"配置文件 {args.config} 不存在")
        print(f"使用 --create-config 创建默认配置文件")
        print(f"示例: python {sys.argv[0]} --create-config")
        return
    
    # 创建并启动蜜罐
    honeypot = SimpleHoneypot(args.config)
    
    try:
        asyncio.run(honeypot.start())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("程序退出")


if __name__ == "__main__":
    main()