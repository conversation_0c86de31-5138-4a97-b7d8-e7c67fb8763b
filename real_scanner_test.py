#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实扫描工具测试蜜罐检测能力
"""

import subprocess
import time
import asyncio
import socket
from main import SimpleHoneypot


def check_nmap_available():
    """检查nmap是否可用"""
    try:
        result = subprocess.run(['nmap', '--version'], 
                              capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False


def check_telnet_available():
    """检查telnet是否可用"""
    try:
        result = subprocess.run(['telnet'], 
                              capture_output=True, text=True, timeout=2)
        return True  # telnet命令存在（即使没有参数也会有输出）
    except:
        return False


def run_nmap_scan(target_host='127.0.0.1', target_ports='22,23,80,443'):
    """运行nmap扫描"""
    print(f"🔍 运行nmap扫描: {target_host}:{target_ports}")
    
    try:
        # TCP连接扫描
        cmd = ['nmap', '-sT', '-p', target_ports, target_host]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        print("nmap扫描结果:")
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("nmap扫描超时")
        return False
    except Exception as e:
        print(f"nmap扫描失败: {e}")
        return False


def run_manual_scan(target_host='127.0.0.1', target_ports=[22, 23, 80, 443]):
    """手动端口扫描"""
    print(f"🔍 运行手动端口扫描: {target_host}")
    
    for port in target_ports:
        try:
            print(f"扫描端口 {port}...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            
            result = sock.connect_ex((target_host, port))
            if result == 0:
                print(f"  端口 {port} 开放")
                
                # 尝试接收banner
                try:
                    sock.settimeout(2)
                    banner = sock.recv(1024)
                    if banner:
                        print(f"  Banner: {banner.decode('utf-8', errors='ignore').strip()}")
                except:
                    print(f"  端口 {port} 无banner")
            else:
                print(f"  端口 {port} 关闭")
                
            sock.close()
            time.sleep(0.5)  # 扫描间隔
            
        except Exception as e:
            print(f"  扫描端口 {port} 失败: {e}")


async def start_honeypot_for_scan_test():
    """启动蜜罐用于扫描测试"""
    honeypot = SimpleHoneypot()
    
    # 配置要测试的端口
    test_ports = [22, 23, 80, 443]
    honeypot.config['ports'] = test_ports
    
    print("🍯 启动蜜罐进行真实扫描测试...")
    
    # 启动蜜罐监听
    available_ports = []
    for port in test_ports:
        if honeypot.check_port_available(port):
            available_ports.append(port)
            await honeypot.start_port_listener(port)
        else:
            print(f"⚠ 端口 {port} 已被占用，跳过")
    
    if available_ports:
        honeypot.running = True
        print(f"✅ 蜜罐启动成功，监听端口: {available_ports}")
        return honeypot, available_ports
    else:
        print("❌ 没有可用端口")
        return None, []


def main():
    """主函数"""
    print("🧪 真实扫描器检测测试")
    print("=" * 50)
    
    async def test_main():
        # 启动蜜罐
        honeypot, available_ports = await start_honeypot_for_scan_test()
        
        if not honeypot or not available_ports:
            print("无法启动蜜罐，测试终止")
            return
        
        # 等待蜜罐完全启动
        await asyncio.sleep(2)
        
        print(f"\n📡 开始扫描测试，目标端口: {available_ports}")
        
        # 检查可用的扫描工具
        if check_nmap_available():
            print("\n🔧 使用nmap进行扫描...")
            ports_str = ','.join(map(str, available_ports))
            run_nmap_scan(target_ports=ports_str)
        else:
            print("\n⚠ nmap不可用，使用手动扫描...")
            run_manual_scan(target_ports=available_ports)
        
        # 等待所有日志写入
        await asyncio.sleep(3)
        
        # 显示检测结果
        print("\n📋 扫描检测结果:")
        print("=" * 30)
        
        # 读取最新的日志条目
        try:
            with open('honeypot.log', 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 显示最后20条日志
            recent_logs = lines[-20:] if len(lines) > 20 else lines
            for line in recent_logs:
                if 'CONNECTION_ESTABLISHED' in line or 'DATA_RECEIVED' in line:
                    print(line.strip())
                    
        except Exception as e:
            print(f"读取日志失败: {e}")
        
        # 停止蜜罐
        await honeypot.stop()
        
        print("\n✅ 测试完成！")
        print("📋 详细日志请查看:")
        print("   - honeypot.log (本地日志)")
        print("   - honeypot_server.log (服务端日志，如果服务端在运行)")
    
    # 运行测试
    asyncio.run(test_main())


if __name__ == "__main__":
    main()
